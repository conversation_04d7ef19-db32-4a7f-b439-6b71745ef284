# DQG Law Firm Website | موقع دار القانون جروب

A professional, modern, and trustworthy bilingual website for DQG Law Firm (دار القانون جروب) built with Node.js, Express, and MongoDB.

## 🌟 Features

### 🌐 Bilingual Support
- **Arabic (Primary)**: Right-to-Left (RTL) layout with Egyptian Arabic dialect (ar-EG)
- **English (Secondary)**: Left-to-Right (LTR) layout
- Dynamic language switching with URL persistence
- Localized content and date formatting

### 🎨 Design & UI
- **Professional & Modern**: Clean, corporate, and authoritative design
- **Responsive**: Fully responsive design for desktop, tablet, and mobile
- **RTL Support**: Complete RTL layout support for Arabic
- **Color Scheme**: Professional palette with deep blue (#1e3a8a) and gold (#f59e0b) accents
- **Typography**: Cairo font for Arabic, modern sans-serif for English

### 🏗️ Architecture
- **Backend**: Node.js with Express.js framework
- **Database**: MongoDB with Mongoose ODM
- **Frontend**: Modern HTML5, CSS3, JavaScript (ES6+)
- **Security**: Helmet.js, CORS, rate limiting
- **Performance**: Compression, lazy loading, optimized images

### 📱 Key Sections
1. **Hero Slider**: 3-slide carousel with professional legal imagery
2. **About Section**: Company introduction with key features
3. **Services**: Legal services showcase with icons
4. **Clients**: Client logos and testimonials
5. **Video Section**: Promotional video with modal popup
6. **Blog**: Latest legal articles and news
7. **Newsletter**: Email subscription form
8. **Footer**: Contact information and quick links

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd NewDQG-V1
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` file with your configuration:
   ```env
   MONGODB_URI=mongodb://localhost:27017/dqg-law-firm
   PORT=3002
   NODE_ENV=development
   ```

4. **Start the server**
   ```bash
   npm start
   ```

5. **Visit the website**
   Open your browser and go to: `http://localhost:3002`

## 📁 Project Structure

```
NewDQG-V1/
├── package.json              # Dependencies and scripts
├── server.js                 # Main server file
├── .env                      # Environment variables
├── config/
│   └── database.js          # Database configuration
├── models/                   # MongoDB models
│   ├── Blog.js              # Blog post model
│   ├── Service.js           # Service model
│   └── Contact.js           # Contact form model
├── routes/                   # Express routes
│   ├── api.js               # API endpoints
│   └── pages.js             # Page routes
├── public/                   # Static files
│   ├── index.html           # Main homepage
│   ├── css/                 # Stylesheets
│   │   ├── main.css         # Main styles
│   │   ├── rtl.css          # RTL-specific styles
│   │   └── responsive.css   # Responsive design
│   ├── js/                  # JavaScript files
│   │   ├── main.js          # Main functionality
│   │   ├── slider.js        # Hero slider
│   │   └── language.js      # Language switching
│   └── images/              # Image assets
└── locales/                 # Translation files
    ├── ar.json              # Arabic translations
    └── en.json              # English translations
```

## 🎯 API Endpoints

### Blog Posts
- `GET /api/blogs` - Get all published blogs
- `GET /api/blogs/featured` - Get featured blogs
- `GET /api/blogs/:slug` - Get single blog by slug

### Services
- `GET /api/services` - Get all active services
- `GET /api/services/featured` - Get featured services

### Contact & Newsletter
- `POST /api/contact` - Submit contact form
- `POST /api/newsletter` - Subscribe to newsletter

## 🌍 Language Support

The website supports dynamic language switching between Arabic and English:

### URL Parameters
- `?lang=ar` - Arabic (default)
- `?lang=en` - English

### Translation Keys
All text content uses translation keys (e.g., `data-key="nav.home"`) that are automatically replaced based on the selected language.

## 🎨 Customization

### Colors
Main colors are defined in CSS variables in `public/css/main.css`:
```css
:root {
    --primary-color: #1e3a8a;    /* Deep blue */
    --secondary-color: #f59e0b;   /* Gold */
    --accent-color: #dc2626;      /* Red */
    --dark-color: #1f2937;        /* Dark gray */
    --light-color: #f8fafc;       /* Light gray */
}
```

### Fonts
- **Arabic**: Cairo font family
- **English**: Inter font family
- **Icons**: Font Awesome 6

## 📱 Responsive Breakpoints

- **Large screens**: 1200px and up
- **Medium screens**: 992px to 1199px
- **Small screens**: 768px to 991px
- **Extra small**: 576px to 767px
- **Mobile**: up to 575px

## 🔧 Development

### Available Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm test` - Run tests (to be implemented)

### Adding New Pages
1. Create HTML file in `public/` directory
2. Add route in `routes/pages.js`
3. Add navigation links in `public/index.html`
4. Add translations in `locales/ar.json` and `locales/en.json`

## 🚀 Deployment

### Production Setup
1. Set `NODE_ENV=production` in environment
2. Configure MongoDB connection string
3. Set up SSL certificates
4. Configure reverse proxy (nginx recommended)
5. Set up process manager (PM2 recommended)

### Environment Variables
```env
NODE_ENV=production
MONGODB_URI=mongodb://your-production-db
PORT=3002
JWT_SECRET=your-secure-secret
EMAIL_HOST=smtp.your-provider.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
SITE_URL=https://your-domain.com
```

## 📄 License

This project is licensed under the ISC License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions, please contact the DQG Law Firm development team.

---

**Built with ❤️ for DQG Law Firm | دار القانون جروب**
