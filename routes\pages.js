const express = require('express');
const path = require('path');
const router = express.Router();

// Serve main pages
router.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

router.get('/about', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/about.html'));
});

router.get('/services', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/services.html'));
});

router.get('/services/:slug', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/service-detail.html'));
});

router.get('/legal-research', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/legal-research.html'));
});

router.get('/legal-training', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/legal-training.html'));
});

router.get('/treasures', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/treasures.html'));
});

router.get('/treasures/:category', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/treasure-category.html'));
});

router.get('/magazine', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/magazine.html'));
});

router.get('/magazine/:category', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/magazine-category.html'));
});

router.get('/blog', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/blog.html'));
});

router.get('/blog/:slug', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/blog-detail.html'));
});

router.get('/contact', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/contact.html'));
});

// Language switching
router.get('/ar/*', (req, res) => {
  const originalPath = req.params[0];
  res.redirect('/' + originalPath + '?lang=ar');
});

router.get('/en/*', (req, res) => {
  const originalPath = req.params[0];
  res.redirect('/' + originalPath + '?lang=en');
});

module.exports = router;

